import { defineAuth } from '@aws-amplify/backend';

/**
 * Define and configure your auth resource
 * @see https://docs.amplify.aws/gen2/build-a-backend/auth
 */
export const auth = defineAuth({
  loginWith: {
    email: true
  },
  userAttributes: {
    email: {
      required: true,
      mutable: true
    },
    phoneNumber: {
      required: false,
      mutable: true
    },
    givenName: {
      required: false,
      mutable: true
    },
    familyName: {
      required: false,
      mutable: true
    }
  },
  multifactor: {
    mode: 'OPTIONAL',
    totp: true,
    sms: true
  },
  accountRecovery: 'EMAIL_ONLY'
});
