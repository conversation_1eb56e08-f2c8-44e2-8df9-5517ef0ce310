'use client';

import React, { createContext, useContext } from 'react';

interface ThemeColors {
  primary: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  };
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    inverse: string;
  };
  border: {
    light: string;
    medium: string;
    dark: string;
  };
  status: {
    success: string;
    warning: string;
    error: string;
    info: string;
  };
}

const themeColors: ThemeColors = {
  primary: {
    50: '#eff6ff',   // Very light blue
    100: '#dbeafe',  // Light blue
    200: '#bfdbfe',  // Lighter blue
    300: '#93c5fd',  // Light blue
    400: '#60a5fa',  // Medium light blue
    500: '#3b82f6',  // Primary blue
    600: '#2563eb',  // Medium blue
    700: '#1d4ed8',  // Dark blue
    800: '#1e40af',  // Darker blue
    900: '#1e3a8a',  // Very dark blue
  },
  background: {
    primary: '#ffffff',    // Pure white
    secondary: '#f8fafc',  // Very light gray-blue
    tertiary: '#f1f5f9',   // Light gray-blue
  },
  text: {
    primary: '#0f172a',    // Very dark blue-gray
    secondary: '#475569',  // Medium blue-gray
    tertiary: '#64748b',   // Light blue-gray
    inverse: '#ffffff',    // White text
  },
  border: {
    light: '#e2e8f0',     // Light blue-gray
    medium: '#cbd5e1',    // Medium blue-gray
    dark: '#94a3b8',      // Dark blue-gray
  },
  status: {
    success: '#10b981',   // Green
    warning: '#f59e0b',   // Amber
    error: '#ef4444',     // Red
    info: '#3b82f6',      // Blue (same as primary)
  },
};

interface ThemeContextType {
  colors: ThemeColors;
  getStatusColor: (status: string) => string;
  getStatusBgColor: (status: string) => string;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const getStatusColor = (status: string): string => {
    switch (status.toUpperCase()) {
      case 'PAID':
      case 'COMPLETED':
      case 'ACTIVE':
        return 'text-emerald-600';
      case 'PENDING':
      case 'UPCOMING':
        return 'text-amber-600';
      case 'FAILED':
      case 'BOUNCED':
      case 'OVERDUE':
        return 'text-red-600';
      case 'INACTIVE':
        return 'text-gray-600';
      default:
        return 'text-blue-600';
    }
  };

  const getStatusBgColor = (status: string): string => {
    switch (status.toUpperCase()) {
      case 'PAID':
      case 'COMPLETED':
      case 'ACTIVE':
        return 'bg-emerald-50 text-emerald-700 border-emerald-200';
      case 'PENDING':
      case 'UPCOMING':
        return 'bg-amber-50 text-amber-700 border-amber-200';
      case 'FAILED':
      case 'BOUNCED':
      case 'OVERDUE':
        return 'bg-red-50 text-red-700 border-red-200';
      case 'INACTIVE':
        return 'bg-gray-50 text-gray-700 border-gray-200';
      default:
        return 'bg-blue-50 text-blue-700 border-blue-200';
    }
  };

  const value = {
    colors: themeColors,
    getStatusColor,
    getStatusBgColor,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
} 