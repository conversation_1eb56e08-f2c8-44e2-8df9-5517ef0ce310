'use client';

import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../../amplify/data/resource';
import PaymentTimeline from '../../components/PaymentTimeline';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../../components/DashboardLayout';

const client = generateClient<Schema>();

interface PaymentScheduleItem {
  id: string;
  amount: number;
  dueDate: string;
  status: 'PAID' | 'PENDING' | 'OVERDUE' | 'UPCOMING';
  paidDate?: string;
}

interface Plan {
  id: string;
  clientName: string;
  procedure: string;
  totalAmount: number;
  status: 'ACTIVE' | 'COMPLETED' | 'CANCELLED' | 'DEFAULTED';
  schedule: PaymentScheduleItem[];
  createdAt: string;
  tenantId: string;
}

export default function PlansPage() {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const { getStatusBgColor } = useTheme();

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      
      // Fetch all plans
      const { data: plansData } = await client.models.Plan.list();
      
      // For each plan, get client info and payment schedule
      const plansWithDetails = await Promise.all(
        (plansData || []).map(async (planData) => {
          // Get client info
          const { data: clientData } = await client.models.Client.get({ id: planData.clientId });
          
          // Get all payments for this plan
          const { data: paymentsData } = await client.models.Payment.list({
            filter: { planId: { eq: planData.id } }
          });
          
          // Convert payments to schedule format
          const schedule: PaymentScheduleItem[] = (paymentsData || [])
            .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
            .map(payment => {
              let status: 'PAID' | 'PENDING' | 'OVERDUE' | 'UPCOMING' = 'UPCOMING';
              
              if (payment.status === 'PAID') {
                status = 'PAID';
              } else if (payment.status === 'PENDING') {
                const dueDate = new Date(payment.dueDate);
                const today = new Date();
                status = dueDate < today ? 'OVERDUE' : 'PENDING';
              } else if (payment.status === 'BOUNCED' || payment.status === 'FAILED') {
                status = 'OVERDUE';
              }
              
              return {
                id: payment.id,
                amount: payment.amount || 0,
                dueDate: payment.dueDate,
                status,
                paidDate: payment.paidAt || undefined
              };
            });
          
          return {
            id: planData.id,
            clientName: clientData?.name || 'Unknown Client',
            procedure: planData.procedure,
            totalAmount: planData.amount || 0,
            status: planData.status || 'ACTIVE',
            createdAt: planData.createdAt || new Date().toISOString(),
            tenantId: planData.tenantId,
            schedule
          };
        })
      );
      
      setPlans(plansWithDetails);
    } catch (error) {
      console.error('Error fetching plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredPlans = plans.filter(plan => {
    const matchesSearch = plan.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         plan.procedure.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === '' || plan.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusStats = () => {
    const active = plans.filter(p => p.status === 'ACTIVE').length;
    const completed = plans.filter(p => p.status === 'COMPLETED').length;
    const cancelled = plans.filter(p => p.status === 'CANCELLED').length;
    const defaulted = plans.filter(p => p.status === 'DEFAULTED').length;
    return { active, completed, cancelled, defaulted };
  };

  const getTotalRevenue = () => {
    return plans.reduce((total, plan) => {
      const paidAmount = plan.schedule
        .filter(payment => payment.status === 'PAID')
        .reduce((sum, payment) => sum + payment.amount, 0);
      return total + paidAmount;
    }, 0);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const stats = getStatusStats();
  const totalRevenue = getTotalRevenue();

  if (loading) {
    return (
      <DashboardLayout activeTab="plans">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-slate-600">Loading payment plans...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout activeTab="plans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-slate-900">Payment Plans</h1>
              <p className="text-slate-600">Monitor and manage all client payment plans</p>
            </div>
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors shadow-sm">
              Create New Plan
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-500">Total Revenue</p>
                <p className="text-xl font-semibold text-slate-900">{formatCurrency(totalRevenue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-500">Active Plans</p>
                <p className="text-xl font-semibold text-slate-900">{stats.active}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-500">Completed</p>
                <p className="text-xl font-semibold text-slate-900">{stats.completed}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-slate-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-500">Cancelled</p>
                <p className="text-xl font-semibold text-slate-900">{stats.cancelled}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-500">Defaulted</p>
                <p className="text-xl font-semibold text-slate-900">{stats.defaulted}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm mb-8">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search by client name or procedure..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select 
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Status</option>
                <option value="ACTIVE">Active</option>
                <option value="COMPLETED">Completed</option>
                <option value="CANCELLED">Cancelled</option>
                <option value="DEFAULTED">Defaulted</option>
              </select>
              <button className="px-4 py-2 text-slate-600 border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Payment Plans Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {filteredPlans.map((plan) => (
            <div key={plan.id} className="relative">
              <div className="absolute top-4 right-4 z-10">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusBgColor(plan.status)}`}>
                  {plan.status}
                </span>
              </div>
              <PaymentTimeline
                planId={plan.id}
                clientName={plan.clientName}
                procedure={plan.procedure}
                totalAmount={plan.totalAmount}
                schedule={plan.schedule}
              />
            </div>
          ))}
        </div>

        {filteredPlans.length === 0 && (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-slate-900">No payment plans found</h3>
            <p className="mt-1 text-sm text-slate-500">
              {searchTerm || statusFilter ? 'Try adjusting your search or filter criteria.' : 'Get started by creating your first payment plan.'}
            </p>
            {!searchTerm && !statusFilter && (
              <div className="mt-6">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                  Create New Plan
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
} 