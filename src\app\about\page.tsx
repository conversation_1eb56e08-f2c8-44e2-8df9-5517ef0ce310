'use client';

import { motion } from 'framer-motion';
import { Heart, Users, Shield, Target, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import MarketingLayout from '../../components/marketing/MarketingLayout';

export default function AboutPage() {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const values = [
    {
      icon: Heart,
      title: "Patient-Centered",
      description: "We believe healthcare should be accessible to everyone, regardless of their financial situation."
    },
    {
      icon: Shield,
      title: "Security First",
      description: "Your patients' data is sacred. We use bank-level encryption and maintain HIPAA compliance."
    },
    {
      icon: Users,
      title: "Clinic-Focused",
      description: "Built by healthcare professionals who understand the unique challenges of running a practice."
    },
    {
      icon: Target,
      title: "Results-Driven",
      description: "We measure our success by your success - increased bookings and happier patients."
    }
  ];

  const stats = [
    { number: "500+", label: "Clinics Served" },
    { number: "$50M+", label: "Processed Safely" },
    { number: "99.9%", label: "Uptime" },
    { number: "4.9/5", label: "Customer Rating" }
  ];

  const team = [
    {
      name: "Dr. <PERSON>",
      role: "Co-Founder & CEO",
      bio: "Former plastic surgeon with 15 years of experience in patient care and practice management."
    },
    {
      name: "<PERSON>",
      role: "Co-Founder & CTO",
      bio: "Former AWS engineer with expertise in healthcare technology and HIPAA compliance."
    },
    {
      name: "Lisa Rodriguez",
      role: "Head of Customer Success",
      bio: "Healthcare operations specialist with 10+ years helping clinics optimize their workflows."
    }
  ];

  return (
    <MarketingLayout>
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <motion.h1
            {...fadeInUp}
            className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6"
          >
            <span className="text-slate-900">Making Healthcare</span>
            <br />
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              More Accessible
            </span>
          </motion.h1>
          
          <motion.p
            {...fadeInUp}
            transition={{ delay: 0.2 }}
            className="text-xl text-slate-600 mb-8 max-w-3xl mx-auto leading-relaxed"
          >
            SurgiFlex was born from a simple belief: financial barriers shouldn't prevent 
            patients from getting the care they need. We're on a mission to make surgical 
            procedures more accessible through flexible financing solutions.
          </motion.p>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div
              {...fadeInUp}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-6">
                Our Story
              </h2>
              <div className="space-y-6 text-slate-600 leading-relaxed">
                <p>
                  Founded in 2023 by a team of healthcare professionals and technology experts, 
                  SurgiFlex emerged from firsthand experience with the challenges patients face 
                  when trying to afford necessary procedures.
                </p>
                <p>
                  Dr. Sarah Johnson, our co-founder, witnessed countless patients delay or 
                  forgo treatments due to financial constraints. Meanwhile, our CTO Michael Chen 
                  saw how outdated payment systems were holding back innovative clinics.
                </p>
                <p>
                  Together, they envisioned a platform that would bridge this gap - making it 
                  easy for clinics to offer flexible payment options while maintaining the 
                  highest standards of security and compliance.
                </p>
              </div>
            </motion.div>

            <motion.div
              {...fadeInUp}
              transition={{ delay: 0.4 }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-purple-100 to-pink-100 rounded-3xl p-8 relative overflow-hidden">
                <div className="bg-white rounded-2xl p-6 shadow-xl">
                  <div className="text-center">
                    <div className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
                      40%
                    </div>
                    <div className="text-slate-600 font-medium mb-4">Increase in Procedure Bookings</div>
                    <div className="text-sm text-slate-500">
                      Average improvement for clinics using SurgiFlex
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gradient-to-br from-slate-50 to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            {...fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-6">
              Our Values
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              These principles guide everything we do, from product development 
              to customer support.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white p-8 rounded-2xl shadow-lg"
              >
                <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center mb-6">
                  <value.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-4">{value.title}</h3>
                <p className="text-slate-600 leading-relaxed">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            {...fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-6">
              Our Impact
            </h2>
            <p className="text-xl text-slate-600">
              Numbers that reflect our commitment to excellence.
            </p>
          </motion.div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
                  {stat.number}
                </div>
                <div className="text-slate-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-gradient-to-br from-slate-50 to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            {...fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-6">
              Meet Our Team
            </h2>
            <p className="text-xl text-slate-600">
              Healthcare professionals and technology experts working together.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white p-8 rounded-2xl shadow-lg text-center"
              >
                <div className="w-20 h-20 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">
                    {member.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-2">{member.name}</h3>
                <p className="text-purple-600 font-medium mb-4">{member.role}</p>
                <p className="text-slate-600 leading-relaxed">{member.bio}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <motion.h2
            {...fadeInUp}
            className="text-4xl lg:text-5xl font-bold text-white mb-6"
          >
            Join Our Mission
          </motion.h2>
          
          <motion.p
            {...fadeInUp}
            transition={{ delay: 0.2 }}
            className="text-xl text-purple-100 mb-8"
          >
            Help us make healthcare more accessible. Start your free trial today.
          </motion.p>

          <motion.div
            {...fadeInUp}
            transition={{ delay: 0.4 }}
          >
            <Link
              href="/auth/signup"
              className="bg-white text-purple-600 px-8 py-4 rounded-full text-lg font-semibold hover:shadow-xl transform hover:scale-105 transition-all duration-300 inline-flex items-center space-x-2"
            >
              <span>Get Started</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
          </motion.div>
        </div>
      </section>
    </MarketingLayout>
  );
}
