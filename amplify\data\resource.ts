import { type ClientSchema, a, defineData } from '@aws-amplify/backend';

/*== SURGIFLEX SCHEMA =====================================================
Multi-tenant SaaS platform for surgical financing with the following models:
- Client: Patient information
- Plan: Financing plans with payment schedules  
- Payment: Individual payment records
- Alert: SMS notifications and bounce alerts
=========================================================================*/
const schema = a.schema({
  Client: a
    .model({
      name: a.string().required(),
      phone: a.string().required(),
      email: a.string(),
      dob: a.date(),
      tenantId: a.string().required(),
      createdAt: a.datetime(),
      updatedAt: a.datetime(),
      plans: a.hasMany('Plan', 'clientId'),
      alerts: a.hasMany('Alert', 'clientId'),
    })
    .authorization((allow) => [
      allow.owner(),
      allow.groups(['Owners', 'Managers', 'Clerks']).to(['read', 'create', 'update']),
    ]),

  Plan: a
    .model({
      clientId: a.id().required(),
      client: a.belongsTo('Client', 'clientId'),
      procedure: a.string().required(),
      amount: a.float().required(),
      status: a.enum(['ACTIVE', 'COMPLETED', 'CANCELLED', 'DEFAULTED']),
      schedule: a.json(), // Array of payment dates
      tenantId: a.string().required(),
      stripeAccountId: a.string(),
      createdAt: a.datetime(),
      updatedAt: a.datetime(),
      payments: a.hasMany('Payment', 'planId'),
    })
    .authorization((allow) => [
      allow.owner(),
      allow.groups(['Owners', 'Managers', 'Clerks']).to(['read', 'create', 'update']),
    ]),

  Payment: a
    .model({
      planId: a.id().required(),
      plan: a.belongsTo('Plan', 'planId'),
      amount: a.float().required(),
      dueDate: a.date().required(),
      status: a.enum(['PENDING', 'PAID', 'FAILED', 'BOUNCED', 'CANCELLED']),
      stripePaymentIntentId: a.string(),
      paidAt: a.datetime(),
      tenantId: a.string().required(),
      createdAt: a.datetime(),
      updatedAt: a.datetime(),
      alerts: a.hasMany('Alert', 'paymentId'),
    })
    .authorization((allow) => [
      allow.owner(),
      allow.groups(['Owners', 'Managers', 'Clerks']).to(['read', 'create', 'update']),
    ]),

  Alert: a
    .model({
      type: a.enum(['REMINDER', 'BOUNCE', 'OVERDUE']),
      clientId: a.id().required(),
      client: a.belongsTo('Client', 'clientId'),
      paymentId: a.id(),
      payment: a.belongsTo('Payment', 'paymentId'),
      message: a.string().required(),
      sentAt: a.datetime(),
      status: a.enum(['SENT', 'FAILED', 'ACKNOWLEDGED']),
      tenantId: a.string().required(),
      createdAt: a.datetime(),
    })
    .authorization((allow) => [
      allow.owner(),
      allow.groups(['Owners', 'Managers', 'Clerks']).to(['read', 'create']),
    ]),
});

export type Schema = ClientSchema<typeof schema>;

export const data = defineData({
  schema,
  authorizationModes: {
    defaultAuthorizationMode: 'userPool',
  },
});

/*== STEP 2 ===============================================================
Go to your frontend source code. From your client-side code, generate a
Data client to make CRUDL requests to your table. (THIS SNIPPET WILL ONLY
WORK IN THE FRONTEND CODE FILE.)

Using JavaScript or Next.js React Server Components, Middleware, Server 
Actions or Pages Router? Review how to generate Data clients for those use
cases: https://docs.amplify.aws/gen2/build-a-backend/data/connect-to-API/
=========================================================================*/

/*
"use client"
import { generateClient } from "aws-amplify/data";
import type { Schema } from "@/amplify/data/resource";

const client = generateClient<Schema>() // use this Data client for CRUDL requests
*/

/*== STEP 3 ===============================================================
Fetch records from the database and use them in your frontend component.
(THIS SNIPPET WILL ONLY WORK IN THE FRONTEND CODE FILE.)
=========================================================================*/

/* For example, in a React component, you can use this snippet in your
  function's RETURN statement */
// const { data: todos } = await client.models.Todo.list()

// return <ul>{todos.map(todo => <li key={todo.id}>{todo.content}</li>)}</ul>
