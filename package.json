{"name": "surgiflex-temp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-amplify/ui-react": "^6.11.2", "aws-amplify": "^6.15.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@aws-amplify/backend": "^1.16.1", "@aws-amplify/backend-cli": "^1.7.2", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "aws-cdk-lib": "^2.189.1", "constructs": "^10.4.2", "esbuild": "^0.25.5", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5.8.3"}}