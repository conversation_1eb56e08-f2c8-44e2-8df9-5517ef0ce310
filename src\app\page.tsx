'use client';

import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../amplify/data/resource';
import { getCurrentUser, signOut } from 'aws-amplify/auth';
import { useTheme } from '../contexts/ThemeContext';
import { seedSampleData } from '../utils/seedData';
import ModernAuth from '../components/ModernAuth';
import DashboardLayout from '../components/DashboardLayout';
import MarketingLayout from '../components/marketing/MarketingLayout';
import LandingPage from '../components/marketing/LandingPage';
import LoadingSpinner from '../components/marketing/LoadingSpinner';
import Link from 'next/link';

const client = generateClient<Schema>();

function Dashboard() {
  const [payments, setPayments] = useState<any[]>([]);
  const [stats, setStats] = useState({
    totalRevenue: 0,
    pendingPayments: 0,
    completedToday: 0,
    bounceAlerts: 0
  });
  const [loading, setLoading] = useState(true);
  const [seeding, setSeeding] = useState(false);
  const { getStatusBgColor } = useTheme();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Check if user is authenticated before making API calls
      try {
        await getCurrentUser();
      } catch (error) {
        // User not authenticated, skip data fetching
        setLoading(false);
        return;
      }

      // Fetch today's payments
      const today = new Date().toISOString().split('T')[0];
      const { data: paymentsData } = await client.models.Payment.list({
        filter: {
          dueDate: { eq: today }
        }
      });

      // Fetch all payments for stats
      const { data: allPayments } = await client.models.Payment.list();

      // Calculate stats
      const totalRevenue = allPayments
        .filter(p => p.status === 'PAID')
        .reduce((sum, p) => sum + (p.amount || 0), 0);

      const pendingPayments = allPayments.filter(p => p.status === 'PENDING').length;
      const completedToday = allPayments.filter(p =>
        p.status === 'PAID' &&
        p.paidAt &&
        new Date(p.paidAt).toDateString() === new Date().toDateString()
      ).length;
      const bounceAlerts = allPayments.filter(p => p.status === 'BOUNCED').length;

      setStats({
        totalRevenue,
        pendingPayments,
        completedToday,
        bounceAlerts
      });

      // Get client info for today's payments
      const paymentsWithClients = await Promise.all(
        (paymentsData || []).map(async (payment) => {
          if (payment.planId) {
            const { data: plan } = await client.models.Plan.get({ id: payment.planId });
            if (plan?.clientId) {
              const { data: clientData } = await client.models.Client.get({ id: plan.clientId });
              return {
                id: payment.id,
                clientName: clientData?.name || 'Unknown Client',
                amount: payment.amount || 0,
                dueDate: payment.dueDate,
                status: payment.status,
                procedure: plan?.procedure || 'Unknown Procedure'
              };
            }
          }
          return null;
        })
      );

      setPayments(paymentsWithClients.filter(Boolean));
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Set empty states on error
      setStats({
        totalRevenue: 0,
        pendingPayments: 0,
        completedToday: 0,
        bounceAlerts: 0
      });
      setPayments([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSeedData = async () => {
    setSeeding(true);
    const success = await seedSampleData();
    if (success) {
      // Refresh dashboard data
      fetchDashboardData();
    }
    setSeeding(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  if (loading) {
    return (
      <DashboardLayout activeTab="dashboard">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-slate-600">Loading dashboard...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout activeTab="dashboard">
      {/* Main Content */}
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Header with Seed Button */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-slate-900">Account Overview</h1>
            <p className="text-slate-600">Monitor your surgical financing performance</p>
          </div>
          <button
            onClick={handleSeedData}
            disabled={seeding}
            className="bg-emerald-600 hover:bg-emerald-700 disabled:bg-emerald-400 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors shadow-sm"
          >
            {seeding ? 'Seeding...' : 'Seed Sample Data'}
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
          <div className="bg-white overflow-hidden shadow-sm rounded-xl border border-slate-200">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-slate-500 truncate">Total Revenue</dt>
                    <dd className="text-xl font-semibold text-slate-900">{formatCurrency(stats.totalRevenue)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow-sm rounded-xl border border-slate-200">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-amber-100 rounded-xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-slate-500 truncate">Pending Payments</dt>
                    <dd className="text-xl font-semibold text-slate-900">{stats.pendingPayments}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow-sm rounded-xl border border-slate-200">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-slate-500 truncate">Completed Today</dt>
                    <dd className="text-xl font-semibold text-slate-900">{stats.completedToday}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow-sm rounded-xl border border-slate-200">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-slate-500 truncate">Bounce Alerts</dt>
                    <dd className="text-xl font-semibold text-slate-900">{stats.bounceAlerts}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Today's Payments */}
        <div className="bg-white shadow-sm rounded-xl border border-slate-200">
          <div className="px-6 py-6 sm:p-8">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl leading-6 font-semibold text-slate-900">Today's Payments</h3>
              <span className="text-sm text-slate-500 bg-slate-100 px-3 py-1 rounded-full">
                {new Date().toLocaleDateString('en-CA', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </span>
            </div>

            {payments.length === 0 ? (
              <div className="text-center py-12">
                <svg className="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-slate-900">No payments due today</h3>
                <p className="mt-1 text-sm text-slate-500">All caught up! Check back tomorrow.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {payments.map((payment) => (
                  <div key={payment.id} className="flex items-center justify-between p-4 border border-slate-200 rounded-xl hover:bg-slate-50 transition-colors">
                    {/* Client Info */}
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                          <span className="text-sm font-semibold text-blue-600">
                            {payment.clientName.split(' ').map((n: string) => n[0]).join('')}
                          </span>
                        </div>
                      </div>
                      <div>
                        <p className="text-sm font-semibold text-slate-900">{payment.clientName}</p>
                        <p className="text-sm text-slate-500">{payment.procedure}</p>
                      </div>
                    </div>

                    {/* Payment Details */}
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm font-semibold text-slate-900">{formatCurrency(payment.amount)}</p>
                        <p className="text-sm text-slate-500">Due {payment.dueDate}</p>
                      </div>

                      {/* Status Pill */}
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusBgColor(payment.status)}`}>
                        {payment.status}
                      </span>

                      {/* Quick Actions */}
                      <div className="flex space-x-2">
                        {payment.status === 'PENDING' && (
                          <button className="text-blue-600 hover:text-blue-800 text-sm font-medium px-3 py-1 rounded-lg hover:bg-blue-50 transition-colors">
                            Send Reminder
                          </button>
                        )}
                        {payment.status === 'BOUNCED' && (
                          <button className="text-red-600 hover:text-red-800 text-sm font-medium px-3 py-1 rounded-lg hover:bg-red-50 transition-colors">
                            Retry Payment
                          </button>
                        )}
                        <button className="text-slate-400 hover:text-slate-600 p-1 rounded-lg hover:bg-slate-100 transition-colors">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* View All Button */}
            <div className="mt-8 text-center">
              <Link href="/plans" className="text-blue-600 hover:text-blue-800 text-sm font-medium px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors">
                View all payments →
              </Link>
            </div>
          </div>
        </div>
    </div>
    </DashboardLayout>
  );
}

export default function Home() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [authError, setAuthError] = useState<string>('');

  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      setLoading(true);
      setAuthError('');

      // Clear any potential cached auth errors
      localStorage.removeItem('amplify-auth-error');

      // Add a small delay to ensure Amplify is fully configured
      await new Promise(resolve => setTimeout(resolve, 200));

      const currentUser = await getCurrentUser();
      console.log('User already signed in:', currentUser.username);
      setUser(currentUser);
    } catch (error: any) {
      console.log('No signed in user, showing auth screen');

      // Handle specific auth configuration errors
      if (error?.message?.includes('UserPool not configured')) {
        setAuthError('Authentication service is initializing. Please wait a moment and refresh the page.');
      } else if (error?.message?.includes('Network Error')) {
        setAuthError('Network connection error. Please check your internet connection.');
      } else if (error?.message?.includes('Auth UserPool not configured')) {
        setAuthError('Authentication is still loading. Please wait a moment...');
        // Retry after a delay
        setTimeout(() => {
          checkAuthState();
        }, 2000);
        return;
      }

      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      setUser(null);
      // Clear any cached auth state and local storage
      localStorage.clear();
      sessionStorage.clear();
      window.location.reload();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const handleAuthSuccess = async () => {
    // Refresh auth state after successful authentication
    await checkAuthState();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-slate-600">Loading SurgiFlex...</p>
        </div>
      </div>
    );
  }

  // Show auth error if configuration is broken
  if (authError) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-red-50 border border-red-200 rounded-xl p-6">
            <svg className="w-12 h-12 text-red-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <h3 className="text-lg font-semibold text-red-900 mb-2">Authentication Error</h3>
            <p className="text-red-800 mb-4">{authError}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Refresh Page
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If user is authenticated, show dashboard
  if (user) {
    return <Dashboard />;
  }

  // If no user, show marketing landing page
  return (
    <MarketingLayout>
      <LandingPage />
    </MarketingLayout>
  );
}
