SurgiFlex — Project Context

1 · Purpose & Vision

SurgiFlex is a multi‑tenant SaaS platform that allows elective‑care clinics (e.g., cosmetic, dental, veterinary, physio) to offer on‑the‑spot financing for patient procedures.The app:

stores patient & clinic data securely,

integrates Stripe Connect + Payment Links for pay‑over‑time plans,

sends SMS reminders for upcoming installments and bounce alerts when a payment fails,

surfaces real‑time payment status to front‑desk staff via an intuitive dashboard.

Clinics subscribe on a per‑location basis; receptionists need zero technical know‑how.  The MVP scope is deliberately lighter than a full CRM so you can build fast, showcase AWS chops, and iterate.

2 · Personas & UX

Persona

Key Goals

Primary Screens

Receptionist/Clerk

‑ Quick search patients  ‑ Start a financing plan ‑ See today’s due payments & bounced alerts

Client Search, New Plan Wizard, Today Tab

Clinic Owner/Admin

‑ Monitor portfolio health  ‑ Export payout reports  ‑ Manage staff accounts

Dashboard, Reports, Settings

Patient (Read‑only)

‑ View schedule & balance  ‑ Update card details

Patient Portal (optional v2)

UX Inspiration (Mobbin)

Stripe Mobile – Dashboard → payment summaries card

Klarna – Pay Later → installment timeline

Square Appointments – Client Card → quick‑action toolbar

Shop Pay – Order Tracking → status pill & progress bar

(Search these keywords on mobbin.com to grab references.)

3 · Core Functional Requirements

Secure Client Repository (create/read/update only within tenant)

Stripe Financing Workflow• Create PaymentIntent(s) or use Stripe In‑Person Financing (future)• Handle webhooks for success, failure, dispute

SMS Engine• T‑1 day & T tag due‑day reminders• Immediate alert on payment failure

Bounce Dashboard showing open issues + retry controls

Multi‑tenant isolation (one AWS account, tenantId partition key)

Receptionist‑friendly quick actions (add client, take payment, resend invoice)

Audit Trail & Activity Log (who created plan, edits, etc.)

Basic role‑based access (Owner, Manager, Clerk)

4 · Non‑Functional

PIPEDA/HIPAA‑aware data handling (encryption at rest & in transit)

<50 ms p95 read latency for dashboard widgets

Zero‑downtime deploys & automatic rollbacks via Canary

Observability: CloudWatch metrics + Sentry (frontend)

5 · Tech Stack (AWS‑Centric)

Layer

Service

Notes

Frontend

Next.js 14 (App Router) + TypeScript

Deployed via AWS Amplify Hosting

Auth

Amazon Cognito

Tenant‑tagged user pool groups

API

AppSync (GraphQL) + Lambda resolvers

Simpler dev‑experience than API Gateway REST

DB

DynamoDB (single‑table, PK = tenantId, SK variants)

Low‑ops, serverless, scales

Payments

Stripe Connect + Webhooks via Lambda

Use Connect Accounts per clinic

Notifications

SNS → SMS (or Pinpoint for high volume)

Template‑driven

Schedulers

EventBridge Scheduler

Fire daily reminder batch

Secrets

AWS Secrets Manager

Stripe keys, Twilio keys

Infra as Code

AWS CDK (TypeScript)

Self‑documenting, deploy from GitHub Actions

CI/CD

GitHub Actions → CDK Pipelines → Amplify

PR previews

Why AWS over Supabase?

Lets you showcase IAM, Lambda, Cognito, and serverless patterns for SAA‑C03 prep.

Fine‑grained HIPAA/PIPEDA compliance controls.

Native SMS (SNS/Pinpoint) eliminates external gateways.

CDK + Amplify keeps it almost as productive as Supabase while avoiding lock‑in.

6 · Data Model (single‑table key schema)

PK (tenantId)

SK

Attributes

TENANT#<id>

META

name, stripeAccountId, timezone

TENANT#<id>

USER#<uid>

role, email, phone

TENANT#<id>

CLIENT#<cid>

fullName, phone, dob, notes

TENANT#<id>

PLAN#<planId>

clientId, procedure, amount, schedule[]

TENANT#<id>

PAYMENT#<pid>

planId, dueDate, status, stripePaymentId

TENANT#<id>

ALERT#<pid>

type (REMINDER

BOUNCE), sentAt

Global Secondary Index GSI1 (SK begins_with PAYMENT# + dueDate) for upcoming reminders.

7 · Key Workflows

7.1 Create Financing Plan

Receptionist selects procedure & amount.

App creates PLAN + schedule entities.

Lambda calls Stripe to create PaymentIntent(s) + webhook secret.

Return hosted payment link (tablet signature flow) or collect card on file.

7.2 Reminder Cron (EventBridge rule @ 02:00 local)

Query GSI for payments where dueDate == today+1.

Publish SNS SMS via templated message.

Log ALERT item for audit.

7.3 Payment Failure Webhook

Stripe → API Gateway → Lambda.

Update PAYMENT.status = "BOUNCED".

Insert ALERT and push SMS.

8 · API Surface (GraphQL examples)

mutation CreateClient($input: CreateClientInput!) { … }
mutation CreatePlan($input: CreatePlanInput!) { … }
query    GetDuePayments($tenantId: ID!, $date: AWSDate) { … }

(See schema.graphql for full list.)

9 · Deployment Workflow

Bootstrap: npx cdk init app --language=typescript

Stacks: CoreStack (Cognito, DynamoDB, AppSync) → NotifStack (SNS, EventBridge) → FrontendStack (Amplify).

GitHub Action: On main push → npm ci && npm run test && cdk deploy.

Preview URLs per feature branch via Amplify.

10 · Milestones & Effort (4‑week sprint)

Week

Deliverable

1

CDK infra, auth, sample GraphQL CRUD

2

Stripe Connect integration & webhooks

3

SMS reminders + bounce handling

4

Polished Mobbin‑inspired UI & tenant onboarding

11 · Open Questions

White‑label sub‑domains per clinic?

Support ACH/direct‑debited plans (US) vs. card‑only?

Patient self‑service portal in MVP or phase 2?

12 · Glossary

Term

Meaning

Bounce

PaymentIntent failed after all automatic retries

Tenant

A single clinic/location subscribing to SurgiFlex

Plan

A structured repayment schedule linked to a procedure