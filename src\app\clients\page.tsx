'use client';

import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../../amplify/data/resource';
import ClientCard from '../../components/ClientCard';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../../components/DashboardLayout';

const client = generateClient<Schema>();

interface BackendClient {
  id: string;
  name: string;
  phone: string;
  email?: string | null;
  dob?: string | null;
  tenantId: string;
  createdAt?: string | null;
  updatedAt?: string | null;
}

interface Client {
  id: string;
  name: string;
  phone: string;
  email?: string;
  dob?: string;
  totalSpent: number;
  activePlans: number;
  lastVisit?: string;
  nextPayment?: {
    amount: number;
    dueDate: string;
  };
  status: 'ACTIVE' | 'INACTIVE' | 'OVERDUE';
}

export default function ClientsPage() {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const { getStatusBgColor } = useTheme();

  useEffect(() => {
    fetchClients();
  }, []);

  const fetchClients = async () => {
    try {
      setLoading(true);
      
      // Fetch all clients
      const { data: clientsData } = await client.models.Client.list();
      
      // For each client, calculate their stats
      const clientsWithStats = await Promise.all(
        (clientsData || []).map(async (clientData) => {
          // Get all plans for this client
          const { data: plans } = await client.models.Plan.list({
            filter: { clientId: { eq: clientData.id } }
          });
          
          // Get all payments for this client's plans
          const allPayments = [];
          for (const plan of plans || []) {
            const { data: payments } = await client.models.Payment.list({
              filter: { planId: { eq: plan.id } }
            });
            allPayments.push(...(payments || []));
          }
          
          // Calculate stats
          const totalSpent = allPayments
            .filter(p => p.status === 'PAID')
            .reduce((sum, p) => sum + (p.amount || 0), 0);
          
          const activePlans = (plans || []).filter(p => p.status === 'ACTIVE').length;
          
          // Find next payment
          const upcomingPayments = allPayments
            .filter(p => p.status === 'PENDING' && p.dueDate)
            .sort((a, b) => new Date(a.dueDate!).getTime() - new Date(b.dueDate!).getTime());
          
          const nextPayment = upcomingPayments[0] ? {
            amount: upcomingPayments[0].amount || 0,
            dueDate: upcomingPayments[0].dueDate!
          } : undefined;
          
          // Determine status
          const hasOverduePayments = allPayments.some(p => 
            p.status === 'BOUNCED' || 
            (p.status === 'PENDING' && p.dueDate && new Date(p.dueDate) < new Date())
          );
          
          let status: 'ACTIVE' | 'INACTIVE' | 'OVERDUE' = 'INACTIVE';
          if (hasOverduePayments) {
            status = 'OVERDUE';
          } else if (activePlans > 0) {
            status = 'ACTIVE';
          }
          
          return {
            id: clientData.id,
            name: clientData.name,
            phone: clientData.phone,
            email: clientData.email || undefined,
            dob: clientData.dob || undefined,
            totalSpent,
            activePlans,
            nextPayment,
            status,
            tenantId: clientData.tenantId,
            createdAt: clientData.createdAt || null,
            updatedAt: clientData.updatedAt || null
          } as Client;
        })
      );
      
      setClients(clientsWithStats);
    } catch (error) {
      console.error('Error fetching clients:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.phone.includes(searchTerm) ||
    client.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddClient = async () => {
    // For now, create a sample client
    try {
      const newClient = await client.models.Client.create({
        name: 'New Client',
        phone: '+****************',
        email: '<EMAIL>',
        tenantId: 'default-tenant' // In real app, this would come from auth context
      });
      
      if (newClient.data) {
        // Refresh the clients list
        fetchClients();
      }
    } catch (error) {
      console.error('Error creating client:', error);
    }
  };

  const handleEditClient = (client: Client) => {
    setSelectedClient(client);
    setShowAddModal(true);
  };

  const handleCreatePlan = (client: Client) => {
    // Navigate to plan creation page
    console.log('Create plan for:', client);
  };

  const handleSendMessage = (client: Client) => {
    // Open SMS modal
    console.log('Send message to:', client);
  };

  const handleViewHistory = (client: Client) => {
    // Navigate to client history page
    console.log('View history for:', client);
  };

  const getStatusStats = () => {
    const active = clients.filter(c => c.status === 'ACTIVE').length;
    const inactive = clients.filter(c => c.status === 'INACTIVE').length;
    const overdue = clients.filter(c => c.status === 'OVERDUE').length;
    return { active, inactive, overdue };
  };

  const stats = getStatusStats();

  if (loading) {
    return (
      <DashboardLayout activeTab="clients">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-slate-600">Loading clients...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout activeTab="clients">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-slate-900">Clients</h1>
            <p className="text-slate-600">Manage your patient database</p>
          </div>
          <button
            onClick={handleAddClient}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors shadow-sm"
          >
            Add New Client
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-500">Total Clients</p>
                <p className="text-2xl font-semibold text-slate-900">{clients.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-500">Active</p>
                <p className="text-2xl font-semibold text-slate-900">{stats.active}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-slate-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-500">Inactive</p>
                <p className="text-2xl font-semibold text-slate-900">{stats.inactive}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-500">Overdue</p>
                <p className="text-2xl font-semibold text-slate-900">{stats.overdue}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm mb-8">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search clients by name, phone, or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select className="px-4 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">All Status</option>
                <option value="ACTIVE">Active</option>
                <option value="INACTIVE">Inactive</option>
                <option value="OVERDUE">Overdue</option>
              </select>
              <button className="px-4 py-2 text-slate-600 border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Client Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredClients.map((client) => (
            <ClientCard
              key={client.id}
              client={client}
              onEdit={handleEditClient}
              onCreatePlan={handleCreatePlan}
              onSendMessage={handleSendMessage}
              onViewHistory={handleViewHistory}
            />
          ))}
        </div>

        {filteredClients.length === 0 && (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-slate-900">No clients found</h3>
            <p className="mt-1 text-sm text-slate-500">
              {searchTerm ? 'Try adjusting your search terms.' : 'Get started by adding your first client.'}
            </p>
            {!searchTerm && (
              <div className="mt-6">
                <button
                  onClick={handleAddClient}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  Add New Client
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
} 