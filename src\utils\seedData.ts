import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../amplify/data/resource';

const client = generateClient<Schema>();

export async function seedSampleData() {
  try {
    console.log('Starting data seeding...');
    
    // Create sample clients
    const clients = [
      {
        name: '<PERSON>',
        phone: '+****************',
        email: '<EMAIL>',
        tenantId: 'default-tenant'
      },
      {
        name: '<PERSON>',
        phone: '+****************',
        email: '<EMAIL>',
        tenantId: 'default-tenant'
      },
      {
        name: '<PERSON>',
        phone: '+****************',
        email: 'emma.rodrig<PERSON><PERSON>@email.com',
        tenantId: 'default-tenant'
      },
      {
        name: '<PERSON>',
        phone: '+****************',
        email: '<EMAIL>',
        tenantId: 'default-tenant'
      }
    ];

    const createdClients = [];
    for (const clientData of clients) {
      const { data: client } = await client.models.Client.create(clientData);
      if (client) {
        createdClients.push(client);
        console.log(`Created client: ${client.name}`);
      }
    }

    // Create sample plans
    const plans = [
      {
        clientId: createdClients[0]?.id,
        procedure: 'Rhinoplasty',
        amount: 2700.00,
        status: 'ACTIVE' as const,
        tenantId: 'default-tenant'
      },
      {
        clientId: createdClients[1]?.id,
        procedure: 'Botox Treatment',
        amount: 1250.00,
        status: 'COMPLETED' as const,
        tenantId: 'default-tenant'
      },
      {
        clientId: createdClients[2]?.id,
        procedure: 'Laser Hair Removal',
        amount: 1800.00,
        status: 'ACTIVE' as const,
        tenantId: 'default-tenant'
      },
      {
        clientId: createdClients[3]?.id,
        procedure: 'Dental Implants',
        amount: 3200.00,
        status: 'ACTIVE' as const,
        tenantId: 'default-tenant'
      }
    ];

    const createdPlans = [];
    for (const planData of plans) {
      if (planData.clientId) {
        const { data: plan } = await client.models.Plan.create(planData);
        if (plan) {
          createdPlans.push(plan);
          console.log(`Created plan: ${plan.procedure} for ${plan.amount}`);
        }
      }
    }

    // Create sample payments
    const today = new Date();
    const payments = [
      // Sarah's Rhinoplasty payments
      {
        planId: createdPlans[0]?.id,
        amount: 450.00,
        dueDate: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
        status: 'PAID' as const,
        paidAt: new Date(today.getTime() - 29 * 24 * 60 * 60 * 1000).toISOString(),
        tenantId: 'default-tenant'
      },
      {
        planId: createdPlans[0]?.id,
        amount: 450.00,
        dueDate: new Date(today.getTime() + 1 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // tomorrow
        status: 'PENDING' as const,
        tenantId: 'default-tenant'
      },
      {
        planId: createdPlans[0]?.id,
        amount: 450.00,
        dueDate: new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
        status: 'PENDING' as const,
        tenantId: 'default-tenant'
      },
      
      // Michael's Botox payments (completed)
      {
        planId: createdPlans[1]?.id,
        amount: 625.00,
        dueDate: new Date(today.getTime() - 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: 'PAID' as const,
        paidAt: new Date(today.getTime() - 59 * 24 * 60 * 60 * 1000).toISOString(),
        tenantId: 'default-tenant'
      },
      {
        planId: createdPlans[1]?.id,
        amount: 625.00,
        dueDate: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: 'PAID' as const,
        paidAt: new Date(today.getTime() - 29 * 24 * 60 * 60 * 1000).toISOString(),
        tenantId: 'default-tenant'
      },
      
      // Emma's Laser Hair Removal (has overdue payment)
      {
        planId: createdPlans[2]?.id,
        amount: 300.00,
        dueDate: new Date(today.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 5 days ago
        status: 'BOUNCED' as const,
        tenantId: 'default-tenant'
      },
      {
        planId: createdPlans[2]?.id,
        amount: 300.00,
        dueDate: today.toISOString().split('T')[0], // today
        status: 'PENDING' as const,
        tenantId: 'default-tenant'
      },
      
      // David's Dental Implants
      {
        planId: createdPlans[3]?.id,
        amount: 800.00,
        dueDate: new Date(today.getTime() - 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: 'PAID' as const,
        paidAt: new Date(today.getTime() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        tenantId: 'default-tenant'
      },
      {
        planId: createdPlans[3]?.id,
        amount: 800.00,
        dueDate: new Date(today.getTime() + 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: 'PENDING' as const,
        tenantId: 'default-tenant'
      }
    ];

    for (const paymentData of payments) {
      if (paymentData.planId) {
        const { data: payment } = await client.models.Payment.create(paymentData);
        if (payment) {
          console.log(`Created payment: $${payment.amount} due ${payment.dueDate} (${payment.status})`);
        }
      }
    }

    console.log('✅ Sample data seeded successfully!');
    return true;
  } catch (error) {
    console.error('❌ Error seeding data:', error);
    return false;
  }
} 