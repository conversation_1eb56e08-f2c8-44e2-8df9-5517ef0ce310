'use client';

import { useState, useEffect } from 'react';
import { signIn, signUp, confirmSignUp, resendSignUpCode, signInWithRedirect, getCurrentUser, signOut } from 'aws-amplify/auth';
import { useTheme } from '../contexts/ThemeContext';

interface ModernAuthProps {
  onSuccess: () => void;
}

type AuthMode = 'signin' | 'signup' | 'confirm' | 'forgot';

export default function ModernAuth({ onSuccess }: ModernAuthProps) {
  const [mode, setMode] = useState<AuthMode>('signin');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmationCode, setConfirmationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const { getStatusBgColor } = useTheme();

  // Check for passkey support
  const [supportsPasskeys, setSupportsPasskeys] = useState(false);
  const [supportsWebAuthn, setSupportsWebAuthn] = useState(false);

  useEffect(() => {
    // Check for WebAuthn/Passkey support
    if (typeof window !== 'undefined') {
      setSupportsWebAuthn(!!window.PublicKeyCredential);
      setSupportsPasskeys(!!window.PublicKeyCredential && 'authenticatorAttachment' in PublicKeyCredential.prototype);
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      if (mode === 'signin') {
        // Check if user is already signed in and sign them out first
        try {
          await getCurrentUser();
          // If we get here, user is already signed in, so sign them out first
          await signOut();
          // Small delay to ensure sign out is complete
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
          // User not signed in, which is what we want for sign in
        }
        
        await signIn({ username: email, password });
        onSuccess();
      } else if (mode === 'signup') {
        await signUp({
          username: email,
          password,
          options: {
            userAttributes: {
              email
            }
          }
        });
        setSuccess('Account created! Please check your email for a confirmation code.');
        setMode('confirm');
      } else if (mode === 'confirm') {
        await confirmSignUp({ username: email, confirmationCode });
        setSuccess('Email confirmed! You can now sign in.');
        setMode('signin');
      }
    } catch (err: any) {
      console.error('Auth error:', err);
      let errorMessage = err.message || 'An error occurred';
      
      // Provide more helpful error messages
      if (errorMessage.includes('UserPool not configured')) {
        errorMessage = 'Authentication service is not properly configured. Please refresh the page and try again.';
      } else if (errorMessage.includes('Network Error')) {
        errorMessage = 'Network connection error. Please check your internet connection and try again.';
      } else if (errorMessage.includes('UserAlreadyAuthenticatedException')) {
        errorMessage = 'You are already signed in. Redirecting to dashboard...';
        // Trigger success to redirect to dashboard
        setTimeout(() => onSuccess(), 1000);
      } else if (errorMessage.includes('NotAuthorizedException')) {
        errorMessage = 'Invalid email or password. Please check your credentials and try again.';
      } else if (errorMessage.includes('UserNotFoundException')) {
        errorMessage = 'No account found with this email. Please sign up first.';
      } else if (errorMessage.includes('CodeMismatchException')) {
        errorMessage = 'Invalid confirmation code. Please check the code and try again.';
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setLoading(true);
      // Temporarily disabled until Google OAuth secrets are configured
      setError('Google sign-in will be available once Google OAuth is configured. For now, please use email sign-in.');
      setLoading(false);
      // await signInWithRedirect({ provider: 'Google' });
    } catch (err: any) {
      setError(err.message || 'Google sign-in failed');
      setLoading(false);
    }
  };

  const handlePasskeySignIn = async () => {
    setError('Passkey authentication coming soon! This feature will be available once your backend is fully deployed.');
  };

  const handleResendCode = async () => {
    try {
      setLoading(true);
      await resendSignUpCode({ username: email });
      setSuccess('Confirmation code resent to your email.');
    } catch (err: any) {
      setError(err.message || 'Failed to resend code');
    } finally {
      setLoading(false);
    }
  };

  const getTitle = () => {
    switch (mode) {
      case 'signin': return 'Welcome back';
      case 'signup': return 'Create your account';
      case 'confirm': return 'Confirm your email';
      case 'forgot': return 'Reset password';
      default: return 'Sign in';
    }
  };

  const getSubtitle = () => {
    switch (mode) {
      case 'signin': return 'Sign in to your SurgiFlex account';
      case 'signup': return 'Get started with SurgiFlex today';
      case 'confirm': return 'Enter the code sent to your email';
      case 'forgot': return 'We\'ll send you a reset link';
      default: return '';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-slate-50 flex">
      {/* Left Side - Branding & Background */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 relative overflow-hidden">
        {/* Medical Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <svg className="w-full h-full" viewBox="0 0 400 400" fill="none">
            {/* Medical Cross Pattern */}
            <defs>
              <pattern id="medical-cross" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
                <rect x="35" y="20" width="10" height="40" fill="white" opacity="0.3"/>
                <rect x="20" y="35" width="40" height="10" fill="white" opacity="0.3"/>
              </pattern>
              <pattern id="heartbeat" x="0" y="0" width="120" height="40" patternUnits="userSpaceOnUse">
                <path d="M0,20 L20,20 L25,10 L30,30 L35,5 L40,35 L45,20 L120,20" stroke="white" strokeWidth="1" fill="none" opacity="0.2"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#medical-cross)"/>
            <rect width="100%" height="100%" fill="url(#heartbeat)"/>
          </svg>
        </div>

        {/* Content */}
        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          <div className="mb-8">
            <img src="/logo.png" alt="SurgiFlex" className="h-72 mb-6 filter brightness-0 invert"/>
            <h1 className="text-4xl font-bold mb-4">Modern Surgical Financing</h1>
            <p className="text-xl text-blue-100 leading-relaxed">
              Streamline payment plans for cosmetic and elective procedures. 
              Help your patients achieve their goals with flexible financing solutions.
            </p>
          </div>
          
          <div className="space-y-4 text-blue-100">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-3 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              HIPAA Compliant & Secure
            </div>
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-3 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Automated Payment Processing
            </div>
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-3 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Real-time SMS Notifications
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Auth Form */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md">
          {/* Mobile Logo */}
          <div className="lg:hidden text-center mb-8">
            <img src="/logo.png" alt="SurgiFlex" className="h-60 mx-auto mb-4"/>
            <p className="text-slate-600">Surgical Financing Platform</p>
          </div>

          {/* Auth Card */}
          <div className="bg-white rounded-2xl shadow-xl border border-slate-200 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-semibold text-slate-900 mb-2">{getTitle()}</h2>
              <p className="text-slate-600">{getSubtitle()}</p>
            </div>

            {/* Error/Success Messages */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <p className="text-red-800 text-sm">{error}</p>
                </div>
              </div>
            )}

            {success && (
              <div className="mb-6 p-4 bg-emerald-50 border border-emerald-200 rounded-xl">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-emerald-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-emerald-800 text-sm">{success}</p>
                </div>
              </div>
            )}

            {/* Modern Authentication Options */}
            {(mode === 'signin' || mode === 'signup') && (
              <div className="space-y-3 mb-6">
                {/* Passkey Sign-In - only show on signin */}
                {mode === 'signin' && supportsPasskeys && (
                  <button
                    onClick={handlePasskeySignIn}
                    disabled={loading}
                    className="w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50"
                  >
                    <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    Sign in with Passkey
                  </button>
                )}

                {/* Google Sign-In/Sign-Up */}
                <button
                  onClick={handleGoogleSignIn}
                  disabled={loading}
                  className="w-full flex items-center justify-center px-4 py-3 bg-white border border-slate-300 text-slate-700 rounded-lg font-medium hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50"
                >
                  <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  {mode === 'signin' ? 'Continue with Google' : 'Sign up with Google'}
                </button>

                {/* Divider */}
                <div className="relative my-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-slate-200"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-white text-slate-500">
                      {mode === 'signin' ? 'or continue with email' : 'or sign up with email'}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-5">
              {mode !== 'confirm' && (
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-slate-700 mb-2">
                    Email address
                  </label>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Enter your email"
                  />
                </div>
              )}

              {(mode === 'signin' || mode === 'signup') && (
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-slate-700 mb-2">
                    Password
                  </label>
                  <div className="relative">
                    <input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      className="w-full px-4 py-3 pr-12 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      placeholder="Enter your password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                    >
                      {showPassword ? (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        </svg>
                      ) : (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {mode === 'confirm' && (
                <div>
                  <label htmlFor="code" className="block text-sm font-medium text-slate-700 mb-2">
                    Confirmation Code
                  </label>
                  <input
                    id="code"
                    type="text"
                    value={confirmationCode}
                    onChange={(e) => setConfirmationCode(e.target.value)}
                    required
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-center text-lg tracking-widest"
                    placeholder="000000"
                    maxLength={6}
                  />
                </div>
              )}

              {mode === 'signin' && (
                <div className="flex items-center justify-between">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={rememberMe}
                      onChange={(e) => setRememberMe(e.target.checked)}
                      className="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-slate-600">Remember me</span>
                  </label>
                  <button
                    type="button"
                    onClick={() => setMode('forgot')}
                    className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Forgot password?
                  </button>
                </div>
              )}

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Processing...
                  </div>
                ) : (
                  <>
                    {mode === 'signin' && 'Sign in'}
                    {mode === 'signup' && 'Create account'}
                    {mode === 'confirm' && 'Confirm email'}
                    {mode === 'forgot' && 'Send reset link'}
                  </>
                )}
              </button>
            </form>

            {/* Footer Links */}
            <div className="mt-6 text-center space-y-4">
              {mode === 'signin' && (
                <p className="text-sm text-slate-600">
                  Don't have an account?{' '}
                  <button
                    onClick={() => setMode('signup')}
                    className="text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Sign up
                  </button>
                </p>
              )}

              {mode === 'signup' && (
                <p className="text-sm text-slate-600">
                  Already have an account?{' '}
                  <button
                    onClick={() => setMode('signin')}
                    className="text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Sign in
                  </button>
                </p>
              )}

              {mode === 'confirm' && (
                <div className="space-y-2">
                  <p className="text-sm text-slate-600">
                    Didn't receive the code?{' '}
                    <button
                      onClick={handleResendCode}
                      disabled={loading}
                      className="text-blue-600 hover:text-blue-700 font-medium disabled:opacity-50"
                    >
                      Resend
                    </button>
                  </p>
                  <p className="text-sm text-slate-600">
                    <button
                      onClick={() => setMode('signin')}
                      className="text-blue-600 hover:text-blue-700 font-medium"
                    >
                      Back to sign in
                    </button>
                  </p>
                </div>
              )}
            </div>

            {/* Security Trust Indicators */}
            <div className="mt-6 pt-6 border-t border-slate-200">
              <div className="flex items-center justify-center space-x-6 text-xs text-slate-500">
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-1 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  256-bit SSL
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-1 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  HIPAA Compliant
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-1 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  SOC 2 Type II
                </div>
              </div>
            </div>
          </div>

          {/* Additional Info */}
          <div className="mt-6 text-center">
            <p className="text-xs text-slate-500">
              By signing in, you agree to our{' '}
              <a href="#" className="text-blue-600 hover:text-blue-700">Terms of Service</a>
              {' '}and{' '}
              <a href="#" className="text-blue-600 hover:text-blue-700">Privacy Policy</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 