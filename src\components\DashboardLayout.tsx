'use client';

import { useState } from 'react';
import Link from 'next/link';
import { signOut } from 'aws-amplify/auth';

interface DashboardLayoutProps {
  children: React.ReactNode;
  activeTab: string;
}

export default function DashboardLayout({ children, activeTab }: DashboardLayoutProps) {
  const [loading, setLoading] = useState(false);

  const handleSignOut = async () => {
    setLoading(true);
    try {
      await signOut();
      window.location.reload();
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'dashboard', label: 'Account Overview', href: '/' },
    { id: 'clients', label: 'Clients', href: '/clients' },
    { id: 'plans', label: 'Payment Plans', href: '/plans' },
    { id: 'reports', label: 'Reports', href: '/reports' },
  ];

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Top Navigation - Stripe Style */}
      <nav className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Top Bar */}
          <div className="flex justify-between items-center h-16">
            {/* Left side - Logo only */}
            <div className="flex items-center">
              <img src="/logo.png" alt="SurgiFlex" className="h-8"/>
            </div>

            {/* Right side - Actions */}
            <div className="flex items-center space-x-3">
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                New Plan
              </button>
              <button
                onClick={handleSignOut}
                disabled={loading}
                className="text-slate-600 hover:text-slate-900 px-3 py-2 text-sm font-medium transition-colors disabled:opacity-50"
              >
                {loading ? 'Signing out...' : 'Sign Out'}
              </button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-8 -mb-px">
            {tabs.map((tab) => (
              <Link
                key={tab.id}
                href={tab.href}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                }`}
              >
                {tab.label}
              </Link>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
} 