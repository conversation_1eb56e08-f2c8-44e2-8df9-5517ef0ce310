import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import { Amplify } from 'aws-amplify';
import outputs from '../../amplify_outputs.json';
import { ThemeProvider } from '../contexts/ThemeContext';
import AmplifyProvider from '../components/AmplifyProvider';

// Configure Amplify with SSR settings
Amplify.configure(outputs, {
  ssr: true
});

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "SurgiFlex - Surgical Financing Platform",
  description: "Multi-tenant SaaS platform for surgical financing with payment plans and client management",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-slate-50`}
      >
        <AmplifyProvider>
          <ThemeProvider>
            {children}
          </ThemeProvider>
        </AmplifyProvider>
      </body>
    </html>
  );
}
