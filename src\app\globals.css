@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --amplify-colors-brand-primary-10: #eff6ff;
  --amplify-colors-brand-primary-20: #dbeafe;
  --amplify-colors-brand-primary-40: #93c5fd;
  --amplify-colors-brand-primary-60: #3b82f6;
  --amplify-colors-brand-primary-80: #1d4ed8;
  --amplify-colors-brand-primary-90: #1e3a8a;
  --amplify-colors-brand-primary-100: #1e3a8a;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Amplify UI customizations */
[data-amplify-authenticator] {
  --amplify-components-authenticator-router-background-color: #f8fafc;
  --amplify-components-authenticator-router-border-color: #e2e8f0;
  --amplify-components-button-primary-background-color: #3b82f6;
  --amplify-components-button-primary-hover-background-color: #2563eb;
  --amplify-components-button-primary-focus-background-color: #1d4ed8;
  --amplify-components-button-primary-active-background-color: #1e40af;
  --amplify-components-tabs-item-active-border-color: #3b82f6;
  --amplify-components-tabs-item-active-color: #1e40af;
  --amplify-components-fieldcontrol-focus-border-color: #3b82f6;
}

[data-amplify-authenticator] [data-amplify-router] {
  background: white;
  border-radius: 1rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

[data-amplify-authenticator] h1,
[data-amplify-authenticator] h2,
[data-amplify-authenticator] h3 {
  color: #0f172a !important;
  font-weight: 600;
}

[data-amplify-authenticator] [data-amplify-button][data-variation="primary"] {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

[data-amplify-authenticator] [data-amplify-button][data-variation="primary"]:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

[data-amplify-authenticator] [data-amplify-field-group] [data-amplify-field] input {
  border-radius: 0.75rem;
  border: 1px solid #cbd5e1;
  transition: all 0.2s ease;
}

[data-amplify-authenticator] [data-amplify-field-group] [data-amplify-field] input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}
