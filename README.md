# SurgiFlex - Surgical Financing Platform

A modern, multi-tenant SaaS platform for surgical financing with payment plans and client management.

## 🌟 **New Marketing Website**

### Features
- **Modern Design**: Klarna-inspired clean aesthetic with purple/pink gradient branding
- **Smooth Animations**: n8n.io-style animations with floating elements and transitions
- **Responsive Layout**: Mobile-first design that works on all devices
- **Complete Pages**: Landing, Features, Pricing, About, Contact, Auth pages

### Marketing Pages
- **Landing Page** (`/`): Hero section, features, testimonials, CTA
- **Features Page** (`/features`): Detailed feature breakdown with benefits
- **Pricing Page** (`/pricing`): Transparent pricing tiers with FAQ
- **About Page** (`/about`): Company story, values, team, impact
- **Contact Page** (`/contact`): Contact form with multiple methods
- **Auth Pages** (`/auth/signin`, `/auth/signup`): Beautiful authentication

## 🎨 **Design Theme**
- **Marketing**: Purple to Pink gradients, modern animations
- **Dashboard**: White & Blue (existing)
- **Inspired by**: <PERSON><PERSON><PERSON> (marketing), Stripe Dashboard, n8n.io animations
- **UI Framework**: Tailwind CSS with Framer Motion

## 🔐 **Authentication Setup**

### Getting Started
1. **Visit**: `http://localhost:3001`
2. **Sign Up**: Click "Create Account" on the Authenticator
3. **Enter Details**: Email and password (email verification required)
4. **Verify Email**: Check your email for verification code
5. **Access Dashboard**: You're in!

### Features
- ✅ Email verification
- ✅ Password reset
- ✅ Session management
- ✅ Secure multi-tenant access

## 🚀 **Development**

### Prerequisites
- Node.js 18+
- AWS Amplify CLI
- Git

### Installation
```bash
npm install
npm run dev
```

### Amplify Backend
```bash
npx ampx sandbox  # Start development backend
```

## 📱 **UI Components**

### Payment Timeline (Klarna Inspired)
- Blue gradient progress bar
- Status indicators (paid, pending, overdue, upcoming)
- Expandable timeline
- Quick action buttons

### Client Cards (Square Appointments Inspired)
- Quick action toolbar
- Status management
- Contact information display
- Financial summary

### Dashboard (Stripe Inspired)
- Revenue statistics cards
- Today's payments overview
- Modern navigation
- Responsive design

## 🏗️ **Architecture**

### Backend (AWS Amplify Gen2)
- **Auth**: Cognito User Pools
- **Data**: DynamoDB with GraphQL API
- **Schema**: Multi-tenant with proper authorization

### Frontend (Next.js 15)
- **Framework**: React with TypeScript
- **Styling**: Tailwind CSS
- **State**: React hooks
- **Theme**: Custom context provider

## 🎯 **Next Steps**

Follow the Development Plan (`docs/Development-Plan.md`) for:
1. ✅ Phase 1: Foundation (Complete)
2. 🔄 Phase 2: Core Features (In Progress)
3. ⏳ Phase 3: Advanced Features
4. ⏳ Phase 4: Production Ready

## 🔗 **Links**
- **Marketing Website**: `http://localhost:3001` (when not authenticated)
- **Dashboard**: `http://localhost:3001` (when authenticated)
- **Features Page**: `http://localhost:3001/features`
- **Pricing Page**: `http://localhost:3001/pricing`
- **About Page**: `http://localhost:3001/about`
- **Contact Page**: `http://localhost:3001/contact`
- **Sign In**: `http://localhost:3001/auth/signin`
- **Sign Up**: `http://localhost:3001/auth/signup`
- **Development Plan**: `docs/Development-Plan.md`
