# SurgiFlex - Surgical Financing Platform

A modern, multi-tenant SaaS platform for surgical financing with payment plans and client management.

## 🎨 **Design Theme**
- **Primary Colors**: White & Blue
- **Inspired by**: Stripe Dashboard, Klarna Payment Timeline, Square Appointments, Shop Pay Order Tracking
- **UI Framework**: Tailwind CSS with custom theme context

## 🔐 **Authentication Setup**

### Getting Started
1. **Visit**: `http://localhost:3001`
2. **Sign Up**: Click "Create Account" on the Authenticator
3. **Enter Details**: Email and password (email verification required)
4. **Verify Email**: Check your email for verification code
5. **Access Dashboard**: You're in!

### Features
- ✅ Email verification
- ✅ Password reset
- ✅ Session management
- ✅ Secure multi-tenant access

## 🚀 **Development**

### Prerequisites
- Node.js 18+
- AWS Amplify CLI
- Git

### Installation
```bash
npm install
npm run dev
```

### Amplify Backend
```bash
npx ampx sandbox  # Start development backend
```

## 📱 **UI Components**

### Payment Timeline (Klarna Inspired)
- Blue gradient progress bar
- Status indicators (paid, pending, overdue, upcoming)
- Expandable timeline
- Quick action buttons

### Client Cards (Square Appointments Inspired)
- Quick action toolbar
- Status management
- Contact information display
- Financial summary

### Dashboard (Stripe Inspired)
- Revenue statistics cards
- Today's payments overview
- Modern navigation
- Responsive design

## 🏗️ **Architecture**

### Backend (AWS Amplify Gen2)
- **Auth**: Cognito User Pools
- **Data**: DynamoDB with GraphQL API
- **Schema**: Multi-tenant with proper authorization

### Frontend (Next.js 15)
- **Framework**: React with TypeScript
- **Styling**: Tailwind CSS
- **State**: React hooks
- **Theme**: Custom context provider

## 🎯 **Next Steps**

Follow the Development Plan (`docs/Development-Plan.md`) for:
1. ✅ Phase 1: Foundation (Complete)
2. 🔄 Phase 2: Core Features (In Progress)
3. ⏳ Phase 3: Advanced Features
4. ⏳ Phase 4: Production Ready

## 🔗 **Links**
- **Dashboard**: `http://localhost:3001`
- **UI Demo**: `http://localhost:3001/demo`
- **Development Plan**: `docs/Development-Plan.md`
