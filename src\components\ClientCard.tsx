'use client';

import { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface Client {
  id: string;
  name: string;
  phone: string;
  email?: string;
  dob?: string;
  totalSpent: number;
  activePlans: number;
  lastVisit?: string;
  nextPayment?: {
    amount: number;
    dueDate: string;
  };
  status: 'ACTIVE' | 'INACTIVE' | 'OVERDUE';
}

interface ClientCardProps {
  client: Client;
  onEdit?: (client: Client) => void;
  onCreatePlan?: (client: Client) => void;
  onSendMessage?: (client: Client) => void;
  onViewHistory?: (client: Client) => void;
}

export default function ClientCard({ 
  client, 
  onEdit, 
  onCreatePlan, 
  onSendMessage, 
  onViewHistory 
}: ClientCardProps) {
  const [showActions, setShowActions] = useState(false);
  const { getStatusBgColor } = useTheme();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-CA', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200">
      {/* Client Header */}
      <div className="p-6 border-b border-slate-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Avatar */}
            <div className="w-14 h-14 bg-blue-100 rounded-xl flex items-center justify-center">
              <span className="text-sm font-semibold text-blue-600">
                {getInitials(client.name)}
              </span>
            </div>
            
            {/* Client Info */}
            <div>
              <h3 className="text-lg font-semibold text-slate-900">{client.name}</h3>
              <div className="flex items-center space-x-3 mt-1">
                <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getStatusBgColor(client.status)}`}>
                  {client.status}
                </span>
                {client.activePlans > 0 && (
                  <span className="text-sm text-slate-500">
                    {client.activePlans} active plan{client.activePlans > 1 ? 's' : ''}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Quick Actions Trigger */}
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-2 text-slate-400 hover:text-slate-600 rounded-xl hover:bg-slate-100 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </button>

            {/* Quick Actions Dropdown */}
            {showActions && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 z-10">
                <div className="py-2">
                  <button
                    onClick={() => {
                      onEdit?.(client);
                      setShowActions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    Edit Client
                  </button>
                  <button
                    onClick={() => {
                      onCreatePlan?.(client);
                      setShowActions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create Plan
                  </button>
                  <button
                    onClick={() => {
                      onSendMessage?.(client);
                      setShowActions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    Send Message
                  </button>
                  <button
                    onClick={() => {
                      onViewHistory?.(client);
                      setShowActions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    View History
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Client Details */}
      <div className="p-6 space-y-4">
        {/* Contact Information */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </div>
            <span className="text-sm text-slate-600 font-medium">{client.phone}</span>
          </div>
          {client.email && (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <span className="text-sm text-slate-600 font-medium">{client.email}</span>
            </div>
          )}
        </div>

        {/* Financial Summary */}
        <div className="grid grid-cols-2 gap-6 pt-4 border-t border-slate-100">
          <div>
            <p className="text-xs text-slate-500 uppercase tracking-wide font-medium">Total Spent</p>
            <p className="text-lg font-semibold text-slate-900 mt-1">{formatCurrency(client.totalSpent)}</p>
          </div>
          {client.nextPayment && (
            <div>
              <p className="text-xs text-slate-500 uppercase tracking-wide font-medium">Next Payment</p>
              <p className="text-lg font-semibold text-slate-900 mt-1">{formatCurrency(client.nextPayment.amount)}</p>
              <p className="text-xs text-slate-500 mt-1">Due {formatDate(client.nextPayment.dueDate)}</p>
            </div>
          )}
        </div>

        {/* Last Visit */}
        {client.lastVisit && (
          <div className="pt-4 border-t border-slate-100">
            <p className="text-xs text-slate-500 uppercase tracking-wide font-medium">Last Visit</p>
            <p className="text-sm text-slate-700 mt-1 font-medium">{formatDate(client.lastVisit)}</p>
          </div>
        )}
      </div>

      {/* Quick Action Buttons */}
      <div className="px-6 pb-6">
        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={() => onCreatePlan?.(client)}
            className="bg-blue-600 text-white px-4 py-3 rounded-xl text-sm font-medium hover:bg-blue-700 transition-colors shadow-sm"
          >
            New Plan
          </button>
          <button
            onClick={() => onSendMessage?.(client)}
            className="border border-slate-300 text-slate-700 px-4 py-3 rounded-xl text-sm font-medium hover:bg-slate-50 transition-colors"
          >
            Message
          </button>
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {showActions && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowActions(false)}
        />
      )}
    </div>
  );
} 