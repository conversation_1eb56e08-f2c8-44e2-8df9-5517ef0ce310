'use client';

import { motion } from 'framer-motion';
import { 
  CreditCard, 
  Shield, 
  Users, 
  Clock, 
  MessageSquare, 
  BarChart3,
  Smartphone,
  Zap,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import Link from 'next/link';
import MarketingLayout from '../../components/marketing/MarketingLayout';

export default function FeaturesPage() {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const features = [
    {
      icon: CreditCard,
      title: "Flexible Payment Plans",
      description: "Create customizable payment schedules that work for your patients' budgets. Support for 3, 6, 12, or custom month plans.",
      benefits: [
        "Multiple payment options",
        "Custom installment schedules",
        "Automatic payment processing",
        "Failed payment retry logic"
      ]
    },
    {
      icon: Shield,
      title: "HIPAA Compliant Security",
      description: "Bank-level encryption and security protocols ensure patient data is always protected and compliant.",
      benefits: [
        "End-to-end encryption",
        "HIPAA compliant infrastructure",
        "Secure data storage",
        "Regular security audits"
      ]
    },
    {
      icon: Users,
      title: "Multi-Tenant Platform",
      description: "Perfect for clinics with multiple locations. Manage everything from one central dashboard with role-based access.",
      benefits: [
        "Multiple clinic locations",
        "Role-based permissions",
        "Centralized management",
        "Staff account management"
      ]
    },
    {
      icon: MessageSquare,
      title: "Automated Communications",
      description: "Keep patients informed with automated SMS reminders and payment notifications.",
      benefits: [
        "SMS payment reminders",
        "Bounce alert notifications",
        "Custom message templates",
        "Multi-language support"
      ]
    },
    {
      icon: BarChart3,
      title: "Real-Time Analytics",
      description: "Monitor your practice's financial health with comprehensive reporting and analytics.",
      benefits: [
        "Revenue tracking",
        "Payment success rates",
        "Patient payment behavior",
        "Export capabilities"
      ]
    },
    {
      icon: Smartphone,
      title: "Mobile-First Design",
      description: "Access your dashboard and manage payments from any device, anywhere.",
      benefits: [
        "Responsive design",
        "Mobile app coming soon",
        "Offline capabilities",
        "Touch-optimized interface"
      ]
    }
  ];

  const integrations = [
    { name: "Stripe", description: "Secure payment processing" },
    { name: "Twilio", description: "SMS notifications" },
    { name: "AWS", description: "Cloud infrastructure" },
    { name: "Cognito", description: "User authentication" }
  ];

  return (
    <MarketingLayout>
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <motion.h1
            {...fadeInUp}
            className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6"
          >
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Powerful Features
            </span>
            <br />
            <span className="text-slate-900">Built for Clinics</span>
          </motion.h1>
          
          <motion.p
            {...fadeInUp}
            transition={{ delay: 0.2 }}
            className="text-xl text-slate-600 mb-8 max-w-2xl mx-auto"
          >
            Everything you need to offer flexible financing to your patients, 
            streamline payments, and grow your practice.
          </motion.p>

          <motion.div
            {...fadeInUp}
            transition={{ delay: 0.4 }}
          >
            <Link
              href="/auth/signup"
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:shadow-xl transform hover:scale-105 transition-all duration-300 inline-flex items-center space-x-2"
            >
              <span>Start Free Trial</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-slate-50 to-purple-50 p-8 rounded-2xl"
              >
                <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center mb-6">
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                
                <h3 className="text-2xl font-bold text-slate-900 mb-4">{feature.title}</h3>
                <p className="text-slate-600 mb-6 leading-relaxed">{feature.description}</p>
                
                <ul className="space-y-3">
                  {feature.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-slate-700">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Integrations Section */}
      <section className="py-20 bg-gradient-to-br from-slate-50 to-purple-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.h2
            {...fadeInUp}
            className="text-3xl lg:text-4xl font-bold text-slate-900 mb-6"
          >
            Seamless Integrations
          </motion.h2>
          
          <motion.p
            {...fadeInUp}
            transition={{ delay: 0.2 }}
            className="text-xl text-slate-600 mb-12"
          >
            Built on top of industry-leading platforms for reliability and security.
          </motion.p>

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {integrations.map((integration, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-sm">
                <h4 className="font-semibold text-slate-900 mb-2">{integration.name}</h4>
                <p className="text-sm text-slate-600">{integration.description}</p>
              </div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <motion.h2
            {...fadeInUp}
            className="text-4xl lg:text-5xl font-bold text-white mb-6"
          >
            Ready to Get Started?
          </motion.h2>
          
          <motion.p
            {...fadeInUp}
            transition={{ delay: 0.2 }}
            className="text-xl text-purple-100 mb-8"
          >
            Join hundreds of clinics already using SurgiFlex.
          </motion.p>

          <motion.div
            {...fadeInUp}
            transition={{ delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link
              href="/auth/signup"
              className="bg-white text-purple-600 px-8 py-4 rounded-full text-lg font-semibold hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              Start Free Trial
            </Link>
            <Link
              href="/contact"
              className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-purple-600 transition-all duration-300"
            >
              Schedule Demo
            </Link>
          </motion.div>
        </div>
      </section>
    </MarketingLayout>
  );
}
