'use client';

import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../../amplify/data/resource';
import { useTheme } from '../../contexts/ThemeContext';
import DashboardLayout from '../../components/DashboardLayout';

const client = generateClient<Schema>();

export default function ReportsPage() {
  const [dateRange, setDateRange] = useState('30');
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState({
    revenue: {
      total: 0,
      thisMonth: 0,
      lastMonth: 0,
      growth: 0
    },
    payments: {
      total: 0,
      successful: 0,
      failed: 0,
      pending: 0
    },
    clients: {
      total: 0,
      active: 0,
      inactive: 0
    }
  });
  const { getStatusBgColor } = useTheme();

  useEffect(() => {
    fetchReportData();
  }, [dateRange]);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      
      // Fetch all payments
      const { data: allPayments } = await client.models.Payment.list();
      
      // Fetch all clients
      const { data: allClients } = await client.models.Client.list();
      
      // Fetch all plans to determine client status
      const { data: allPlans } = await client.models.Plan.list();
      
      // Calculate revenue metrics
      const now = new Date();
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
      
      const totalRevenue = (allPayments || [])
        .filter(p => p.status === 'PAID')
        .reduce((sum, p) => sum + (p.amount || 0), 0);
      
      const thisMonthRevenue = (allPayments || [])
        .filter(p => 
          p.status === 'PAID' && 
          p.paidAt && 
          new Date(p.paidAt) >= thisMonthStart
        )
        .reduce((sum, p) => sum + (p.amount || 0), 0);
      
      const lastMonthRevenue = (allPayments || [])
        .filter(p => 
          p.status === 'PAID' && 
          p.paidAt && 
          new Date(p.paidAt) >= lastMonthStart && 
          new Date(p.paidAt) <= lastMonthEnd
        )
        .reduce((sum, p) => sum + (p.amount || 0), 0);
      
      const growth = lastMonthRevenue > 0 
        ? ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 
        : 0;
      
      // Calculate payment metrics
      const totalPayments = (allPayments || []).length;
      const successfulPayments = (allPayments || []).filter(p => p.status === 'PAID').length;
      const failedPayments = (allPayments || []).filter(p => 
        p.status === 'FAILED' || p.status === 'BOUNCED'
      ).length;
      const pendingPayments = (allPayments || []).filter(p => p.status === 'PENDING').length;
      
      // Calculate client metrics
      const totalClients = (allClients || []).length;
      const activeClients = (allClients || []).filter(clientData => {
        const clientPlans = (allPlans || []).filter(p => p.clientId === clientData.id);
        return clientPlans.some(p => p.status === 'ACTIVE');
      }).length;
      const inactiveClients = totalClients - activeClients;
      
      setReportData({
        revenue: {
          total: totalRevenue,
          thisMonth: thisMonthRevenue,
          lastMonth: lastMonthRevenue,
          growth: Math.round(growth * 10) / 10
        },
        payments: {
          total: totalPayments,
          successful: successfulPayments,
          failed: failedPayments,
          pending: pendingPayments
        },
        clients: {
          total: totalClients,
          active: activeClients,
          inactive: inactiveClients
        }
      });
    } catch (error) {
      console.error('Error fetching report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  if (loading) {
    return (
      <DashboardLayout activeTab="reports">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-slate-600">Loading reports...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout activeTab="reports">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-slate-900">Reports & Analytics</h1>
              <p className="text-slate-600">Track your clinic's financial performance and client metrics</p>
            </div>
            <div className="flex items-center space-x-3">
              <select 
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                className="px-4 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="365">Last year</option>
              </select>
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors shadow-sm">
                Export Report
              </button>
            </div>
          </div>
        </div>

        {/* Revenue Overview */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-slate-900 mb-4">Revenue Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center">
                  <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-500">Total Revenue</p>
                  <p className="text-2xl font-semibold text-slate-900">{formatCurrency(reportData.revenue.total)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-500">This Month</p>
                  <p className="text-2xl font-semibold text-slate-900">{formatCurrency(reportData.revenue.thisMonth)}</p>
                  <p className="text-sm text-emerald-600">+{reportData.revenue.growth}% from last month</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-slate-100 rounded-xl flex items-center justify-center">
                  <svg className="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-500">Last Month</p>
                  <p className="text-2xl font-semibold text-slate-900">{formatCurrency(reportData.revenue.lastMonth)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-500">Avg. Plan Value</p>
                  <p className="text-2xl font-semibold text-slate-900">{formatCurrency(reportData.revenue.total / 32)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Analytics */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-slate-900 mb-4">Payment Analytics</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">Payment Status Breakdown</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-emerald-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-slate-700">Successful</span>
                  </div>
                  <div className="text-right">
                    <span className="text-sm font-semibold text-slate-900">{reportData.payments.successful}</span>
                    <span className="text-xs text-slate-500 ml-2">
                      ({Math.round((reportData.payments.successful / reportData.payments.total) * 100)}%)
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-slate-700">Failed</span>
                  </div>
                  <div className="text-right">
                    <span className="text-sm font-semibold text-slate-900">{reportData.payments.failed}</span>
                    <span className="text-xs text-slate-500 ml-2">
                      ({Math.round((reportData.payments.failed / reportData.payments.total) * 100)}%)
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-amber-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-slate-700">Pending</span>
                  </div>
                  <div className="text-right">
                    <span className="text-sm font-semibold text-slate-900">{reportData.payments.pending}</span>
                    <span className="text-xs text-slate-500 ml-2">
                      ({Math.round((reportData.payments.pending / reportData.payments.total) * 100)}%)
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">Client Status</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-slate-700">Active Clients</span>
                  </div>
                  <div className="text-right">
                    <span className="text-sm font-semibold text-slate-900">{reportData.clients.active}</span>
                    <span className="text-xs text-slate-500 ml-2">
                      ({Math.round((reportData.clients.active / reportData.clients.total) * 100)}%)
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-slate-400 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-slate-700">Inactive Clients</span>
                  </div>
                  <div className="text-right">
                    <span className="text-sm font-semibold text-slate-900">{reportData.clients.inactive}</span>
                    <span className="text-xs text-slate-500 ml-2">
                      ({Math.round((reportData.clients.inactive / reportData.clients.total) * 100)}%)
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-emerald-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-slate-700">Total Clients</span>
                  </div>
                  <div className="text-right">
                    <span className="text-sm font-semibold text-slate-900">{reportData.clients.total}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Coming Soon */}
        <div className="bg-white p-8 rounded-xl border border-slate-200 shadow-sm text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-slate-900 mb-2">Advanced Analytics Coming Soon</h3>
          <p className="text-slate-600 mb-6 max-w-2xl mx-auto">
            We're working on advanced reporting features including revenue forecasting, 
            client lifetime value analysis, and payment trend predictions.
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm text-slate-500">
            <span className="bg-slate-100 px-3 py-1 rounded-full">📊 Revenue Forecasting</span>
            <span className="bg-slate-100 px-3 py-1 rounded-full">📈 Trend Analysis</span>
            <span className="bg-slate-100 px-3 py-1 rounded-full">💰 LTV Calculations</span>
            <span className="bg-slate-100 px-3 py-1 rounded-full">📋 Custom Reports</span>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
} 