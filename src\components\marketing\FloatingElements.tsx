'use client';

import { motion } from 'framer-motion';
import { 
  Heart, 
  Shield, 
  CreditCard, 
  Users, 
  Clock, 
  Star,
  Zap,
  TrendingUp
} from 'lucide-react';

export default function FloatingElements() {
  const elements = [
    { 
      icon: Heart, 
      color: 'text-red-500', 
      bg: 'bg-red-100',
      delay: 0,
      x: '10%',
      y: '20%'
    },
    { 
      icon: Shield, 
      color: 'text-green-500', 
      bg: 'bg-green-100',
      delay: 1,
      x: '80%',
      y: '15%'
    },
    { 
      icon: CreditCard, 
      color: 'text-blue-500', 
      bg: 'bg-blue-100',
      delay: 2,
      x: '15%',
      y: '70%'
    },
    { 
      icon: Users, 
      color: 'text-purple-500', 
      bg: 'bg-purple-100',
      delay: 3,
      x: '85%',
      y: '75%'
    },
    { 
      icon: Clock, 
      color: 'text-yellow-500', 
      bg: 'bg-yellow-100',
      delay: 4,
      x: '50%',
      y: '10%'
    },
    { 
      icon: Star, 
      color: 'text-pink-500', 
      bg: 'bg-pink-100',
      delay: 5,
      x: '20%',
      y: '45%'
    },
    { 
      icon: Zap, 
      color: 'text-orange-500', 
      bg: 'bg-orange-100',
      delay: 6,
      x: '75%',
      y: '45%'
    },
    { 
      icon: TrendingUp, 
      color: 'text-emerald-500', 
      bg: 'bg-emerald-100',
      delay: 7,
      x: '45%',
      y: '80%'
    }
  ];

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
      {elements.map((element, index) => (
        <motion.div
          key={index}
          className="absolute"
          style={{ left: element.x, top: element.y }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ 
            opacity: [0, 0.6, 0.3, 0.6, 0.3],
            scale: [0, 1, 1.2, 1, 1.1],
            rotate: [0, 180, 360],
            y: [-20, 20, -20]
          }}
          transition={{
            duration: 8,
            delay: element.delay * 0.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <div className={`w-12 h-12 ${element.bg} rounded-full flex items-center justify-center shadow-lg backdrop-blur-sm`}>
            <element.icon className={`w-6 h-6 ${element.color}`} />
          </div>
        </motion.div>
      ))}
      
      {/* Gradient orbs */}
      <motion.div
        className="absolute top-1/4 left-1/3 w-64 h-64 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.2, 1],
          rotate: [0, 180, 360],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear"
        }}
      />
      
      <motion.div
        className="absolute bottom-1/4 right-1/3 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"
        animate={{
          scale: [1.2, 1, 1.2],
          rotate: [360, 180, 0],
        }}
        transition={{
          duration: 25,
          repeat: Infinity,
          ease: "linear"
        }}
      />
    </div>
  );
}
