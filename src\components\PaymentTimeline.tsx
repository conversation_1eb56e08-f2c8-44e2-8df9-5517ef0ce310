'use client';

import { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface PaymentScheduleItem {
  id: string;
  amount: number;
  dueDate: string;
  status: 'PAID' | 'PENDING' | 'OVERDUE' | 'UPCOMING';
  paidDate?: string;
}

interface PaymentTimelineProps {
  planId: string;
  clientName: string;
  procedure: string;
  totalAmount: number;
  schedule: PaymentScheduleItem[];
}

export default function PaymentTimeline({ 
  planId, 
  clientName, 
  procedure, 
  totalAmount, 
  schedule 
}: PaymentTimelineProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { getStatusColor } = useTheme();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-CA', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PAID':
        return (
          <div className="w-10 h-10 bg-emerald-500 rounded-xl flex items-center justify-center shadow-sm">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case 'PENDING':
        return (
          <div className="w-10 h-10 bg-amber-500 rounded-xl flex items-center justify-center shadow-sm">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      case 'OVERDUE':
        return (
          <div className="w-10 h-10 bg-red-500 rounded-xl flex items-center justify-center shadow-sm">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        );
      case 'UPCOMING':
        return (
          <div className="w-10 h-10 bg-slate-300 rounded-xl flex items-center justify-center shadow-sm">
            <div className="w-4 h-4 bg-slate-500 rounded-full"></div>
          </div>
        );
      default:
        return null;
    }
  };

  const paidAmount = schedule
    .filter(item => item.status === 'PAID')
    .reduce((sum, item) => sum + item.amount, 0);

  const progressPercentage = (paidAmount / totalAmount) * 100;

  return (
    <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h3 className="text-xl font-semibold text-slate-900">{clientName}</h3>
          <p className="text-sm text-slate-500 mt-1">{procedure}</p>
        </div>
        <div className="text-right">
          <p className="text-xl font-semibold text-slate-900">{formatCurrency(totalAmount)}</p>
          <p className="text-sm text-slate-500">{schedule.length} payments</p>
        </div>
      </div>

      {/* Progress Bar - Blue Gradient */}
      <div className="mb-8">
        <div className="flex justify-between text-sm text-slate-600 mb-3">
          <span className="font-medium">Payment Progress</span>
          <span className="font-medium">{formatCurrency(paidAmount)} of {formatCurrency(totalAmount)}</span>
        </div>
        <div className="w-full bg-slate-200 rounded-full h-3 shadow-inner">
          <div 
            className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 shadow-sm"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-slate-500 mt-2">
          <span>0%</span>
          <span className="font-medium">{Math.round(progressPercentage)}% Complete</span>
          <span>100%</span>
        </div>
      </div>

      {/* Payment Schedule */}
      <div className="space-y-6">
        {schedule.slice(0, isExpanded ? schedule.length : 3).map((payment, index) => (
          <div key={payment.id} className="relative">
            <div className="flex items-center space-x-6">
              {/* Status Icon */}
              <div className="flex-shrink-0">
                {getStatusIcon(payment.status)}
              </div>

              {/* Payment Details */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-semibold text-slate-900">
                      Payment {index + 1}
                    </p>
                    <p className={`text-sm mt-1 ${getStatusColor(payment.status)}`}>
                      {payment.status === 'PAID' && payment.paidDate 
                        ? `Paid on ${formatDate(payment.paidDate)}`
                        : `Due ${formatDate(payment.dueDate)}`
                      }
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-semibold text-slate-900">
                      {formatCurrency(payment.amount)}
                    </p>
                    {payment.status === 'PENDING' && (
                      <button className="text-xs text-blue-600 hover:text-blue-800 mt-1 px-2 py-1 rounded-lg hover:bg-blue-50 transition-colors">
                        Send reminder
                      </button>
                    )}
                    {payment.status === 'OVERDUE' && (
                      <button className="text-xs text-red-600 hover:text-red-800 mt-1 px-2 py-1 rounded-lg hover:bg-red-50 transition-colors">
                        Follow up
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Connection Line */}
            {index < (isExpanded ? schedule.length - 1 : Math.min(2, schedule.length - 1)) && (
              <div className="absolute left-5 top-12 w-0.5 h-8 bg-slate-200"></div>
            )}
          </div>
        ))}
      </div>

      {/* Expand/Collapse Button */}
      {schedule.length > 3 && (
        <div className="mt-8 text-center">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            {isExpanded ? 'Show less' : `Show all ${schedule.length} payments`}
          </button>
        </div>
      )}

      {/* Quick Actions */}
      <div className="mt-8 pt-6 border-t border-slate-200">
        <div className="flex space-x-3">
          <button className="flex-1 bg-blue-600 text-white px-4 py-3 rounded-xl text-sm font-medium hover:bg-blue-700 transition-colors shadow-sm">
            Send Payment Link
          </button>
          <button className="flex-1 border border-slate-300 text-slate-700 px-4 py-3 rounded-xl text-sm font-medium hover:bg-slate-50 transition-colors">
            View Details
          </button>
        </div>
      </div>
    </div>
  );
} 