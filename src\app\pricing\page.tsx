'use client';

import { motion } from 'framer-motion';
import { <PERSON><PERSON>ir<PERSON>, ArrowR<PERSON>, Star } from 'lucide-react';
import Link from 'next/link';
import MarketingLayout from '../../components/marketing/MarketingLayout';

export default function PricingPage() {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const plans = [
    {
      name: "Starter",
      price: "$99",
      period: "per location/month",
      description: "Perfect for single-location clinics getting started",
      features: [
        "Up to 100 patients",
        "Basic payment plans",
        "SMS notifications",
        "Email support",
        "Basic reporting",
        "Stripe integration"
      ],
      cta: "Start Free Trial",
      popular: false
    },
    {
      name: "Professional",
      price: "$199",
      period: "per location/month",
      description: "Ideal for growing practices with advanced needs",
      features: [
        "Up to 500 patients",
        "Advanced payment plans",
        "SMS & email notifications",
        "Priority support",
        "Advanced analytics",
        "Custom branding",
        "Multi-user access",
        "API access"
      ],
      cta: "Start Free Trial",
      popular: true
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "contact us",
      description: "For large practices and multi-location clinics",
      features: [
        "Unlimited patients",
        "Custom payment workflows",
        "White-label solution",
        "Dedicated support",
        "Custom integrations",
        "Advanced security",
        "Training & onboarding",
        "SLA guarantee"
      ],
      cta: "Contact Sales",
      popular: false
    }
  ];

  const faqs = [
    {
      question: "Is there a setup fee?",
      answer: "No, there are no setup fees. You only pay the monthly subscription fee per location."
    },
    {
      question: "Can I cancel anytime?",
      answer: "Yes, you can cancel your subscription at any time. There are no long-term contracts or cancellation fees."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept all major credit cards and ACH payments. All payments are processed securely through Stripe."
    },
    {
      question: "Is my data secure?",
      answer: "Absolutely. We use bank-level encryption and are fully HIPAA compliant. Your data is stored securely on AWS."
    },
    {
      question: "Do you offer training?",
      answer: "Yes, we provide comprehensive onboarding and training for all plans. Enterprise customers get dedicated training sessions."
    }
  ];

  return (
    <MarketingLayout>
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <motion.h1
            {...fadeInUp}
            className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6"
          >
            <span className="text-slate-900">Simple, Transparent</span>
            <br />
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Pricing
            </span>
          </motion.h1>
          
          <motion.p
            {...fadeInUp}
            transition={{ delay: 0.2 }}
            className="text-xl text-slate-600 mb-8 max-w-2xl mx-auto"
          >
            Choose the plan that fits your practice. All plans include a 14-day free trial 
            with no setup fees or long-term contracts.
          </motion.p>

          <motion.div
            {...fadeInUp}
            transition={{ delay: 0.4 }}
            className="flex items-center justify-center space-x-2 text-sm text-slate-600"
          >
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span>14-day free trial</span>
            <span className="text-slate-400">•</span>
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span>No setup fees</span>
            <span className="text-slate-400">•</span>
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span>Cancel anytime</span>
          </motion.div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`relative bg-white rounded-2xl shadow-lg border-2 p-8 ${
                  plan.popular 
                    ? 'border-purple-500 transform scale-105' 
                    : 'border-slate-200'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center space-x-1">
                      <Star className="w-4 h-4" />
                      <span>Most Popular</span>
                    </div>
                  </div>
                )}

                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-slate-900 mb-2">{plan.name}</h3>
                  <p className="text-slate-600 mb-4">{plan.description}</p>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-slate-900">{plan.price}</span>
                    {plan.price !== "Custom" && (
                      <span className="text-slate-600 ml-2">{plan.period}</span>
                    )}
                  </div>
                </div>

                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-slate-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Link
                  href={plan.cta === "Contact Sales" ? "/contact" : "/auth/signup"}
                  className={`w-full py-4 px-6 rounded-full text-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:shadow-xl transform hover:scale-105'
                      : 'border-2 border-slate-300 text-slate-700 hover:border-purple-600 hover:text-purple-600'
                  }`}
                >
                  <span>{plan.cta}</span>
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gradient-to-br from-slate-50 to-purple-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            {...fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-slate-600">
              Have questions? We have answers.
            </p>
          </motion.div>

          <div className="space-y-8">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white p-6 rounded-xl shadow-sm"
              >
                <h3 className="text-lg font-semibold text-slate-900 mb-3">{faq.question}</h3>
                <p className="text-slate-600 leading-relaxed">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <motion.h2
            {...fadeInUp}
            className="text-4xl lg:text-5xl font-bold text-white mb-6"
          >
            Ready to Transform Your Practice?
          </motion.h2>
          
          <motion.p
            {...fadeInUp}
            transition={{ delay: 0.2 }}
            className="text-xl text-purple-100 mb-8"
          >
            Start your free trial today. No credit card required.
          </motion.p>

          <motion.div
            {...fadeInUp}
            transition={{ delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link
              href="/auth/signup"
              className="bg-white text-purple-600 px-8 py-4 rounded-full text-lg font-semibold hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              Start Free Trial
            </Link>
            <Link
              href="/contact"
              className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-purple-600 transition-all duration-300"
            >
              Contact Sales
            </Link>
          </motion.div>
        </div>
      </section>
    </MarketingLayout>
  );
}
