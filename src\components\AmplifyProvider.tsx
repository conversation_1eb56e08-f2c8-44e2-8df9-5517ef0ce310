'use client';

import { useEffect, useState } from 'react';
import { Amplify } from 'aws-amplify';
import outputs from '../../amplify_outputs.json';

export default function AmplifyProvider({ children }: { children: React.ReactNode }) {
  const [isConfigured, setIsConfigured] = useState(false);
  const [configError, setConfigError] = useState<string>('');

  useEffect(() => {
    try {
      // Validate configuration before applying
      if (!outputs.auth?.user_pool_id || !outputs.auth?.user_pool_client_id) {
        throw new Error('Invalid Amplify configuration: Missing auth settings');
      }

      // Configure Amplify on the client side
      Amplify.configure(outputs, { ssr: true });
      
      // Add a small delay to ensure configuration is applied
      setTimeout(() => {
        setIsConfigured(true);
      }, 100);
      
      console.log('Amplify configured successfully');
    } catch (error: any) {
      console.error('Amplify configuration error:', error);
      setConfigError(error.message || 'Failed to configure authentication');
    }
  }, []);

  if (configError) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-red-50 border border-red-200 rounded-xl p-6">
            <svg className="w-12 h-12 text-red-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <h3 className="text-lg font-semibold text-red-900 mb-2">Configuration Error</h3>
            <p className="text-red-800 mb-4">{configError}</p>
            <button 
              onClick={() => window.location.reload()}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Refresh Page
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!isConfigured) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-slate-600">Initializing authentication...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
} 